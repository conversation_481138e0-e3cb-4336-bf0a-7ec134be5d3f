/**
 * 
 */
package edu.odu.cs.cs361;

//import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.hamcrest.MatcherAssert.assertThat; 
import static org.hamcrest.Matchers.*;



/**
 * <AUTHOR>
 *
 */
public class TestCountedStrings {

    @Test
    public void testConstructorString() {
        CountedStrings ct = new CountedStrings();
        assertThat(ct.size(), is(0));
        assertThat(ct.countOf("a"), is(0));
    }

    @Test
    public void testAddString() {
        CountedStrings ct = new CountedStrings();
        ct.add("abc");
        ct.add("def");
        ct.add("ghi");
        ct.add("abc");
        ct.add("def");
        ct.add("abc");
        
        assertThat(ct.size(), is(3));
        assertThat(ct.countOf("a"), is(0));
        assertThat(ct.countOf("abc"), is(3));
        assertThat(ct.countOf("def"), is(2));
        assertThat(ct.countOf("ghi"), is(1));

    }


}
