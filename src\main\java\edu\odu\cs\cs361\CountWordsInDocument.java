package edu.odu.cs.cs361;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Scanner;

public class CountWordsInDocument {

    /**
     * Count words in a text document. the document can be supplied to standard
     * input or as a file with a path given as a command line argument.
     * @param args a path to a text file
     * @throws IOException if the input cannot be read
     */
    public static void main(String[] args) throws IOException {
        BufferedReader in;
        if (args.length > 0) {
            in = new BufferedReader(new FileReader(args[0]));
        } else {
            in = new BufferedReader(new InputStreamReader(System.in));
        }
        new CountWordsInDocument().run(in);
    }

    private class Item {
        public String word;
        public int count;

        public Item() {
            count = -1;
            word = "???";
        }
    }

    private void run(BufferedReader in) throws IOException {
        CountedStrings countedStrings = new CountedStrings();

        // Scan and count the words
        Scanner input = new Scanner(in);
        while (input.hasNext()) {
            String word = input.next();
            countedStrings.add(word.toLowerCase());
        }
        input.close();
        
        report3MostCommonWords(countedStrings);
    }

    private void report3MostCommonWords(CountedStrings countedStrings) {
        System.out.println("The word 'the' occurred " + countedStrings.countOf("the") + " times.");
        /***  Uncomment this block once iterator() has been declared
        // Set up storage for the 3 most common words (plus one "working" slot).
        Item[] best = new Item[4];
        for (int i = 0; i < 4; ++i)  best[i] = new Item();

        // Find the three most common words.
        for (String word: countedStrings) {
            // Stick this word into the working slot
            best[3].word = word;
            best[3].count = countedStrings.countOf(word);
            // Slide this word upward in the list past any less common words.
            int k = 3;
            while (k > 0 && best[k].count > best[k-1].count) {
                // Exchange best[k] and best[k-1]
                Item temp = best[k];
                best[k] = best[k-1];
                best[k-1] = temp;
                --k;
            }
        }

        System.out.println("The 3 most common words are:");
        for (int i = 0; i < 3; ++i) {
            System.out.println("  " + best[i].word + " (" + best[i].count + ")");
        }
        ***/
    }
}