package edu.odu.cs.cs361;

/**
 * A class for counting the number of distinct items added.
 */
public class CountedStrings {

    /**
     * We'll use an array of these to track the items seen and how many times
     * we have seen each one.
     */
    private static class Item {
        public String data;
        public int count;

        public Item (String d) {
            data = d;
            count = 1;
        }
    }

    private Item[] items;
    private int theSize;

    /**
     * Create an empty collection.
     */
    public CountedStrings() {
        items = new Item[60000];
        theSize = 0;
    }

    /**
     * The number of distinct items.
     * 
     * @return how many distinct items have been added?
     */
    public int size() {
        return theSize;
    }

    /**
     * Add an item to the collection.
     * 
     * @param item new item
     */
    public void add(String item) {
        boolean found = false;
        for (int i = 0; (!found) && i < theSize; ++i)
            if (item.equals(items[i].data)) {
                ++ items[i].count;
                found = true;
            }
        if (!found) {
            items[theSize] = new Item(item);
            ++theSize;
        }
    }

    /**
     * How many times has this item been added to the collection?
     * @param item an item to search for
     * @return the number of times that item has been added.
     */
    public int countOf(String item) {
        for (int i = 0; i < theSize; ++i)
            if (item.equals(items[i].data))
                return items[i].count;
        return 0;
    }



}
